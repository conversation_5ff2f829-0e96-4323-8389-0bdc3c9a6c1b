/**
 * Product Video Player Styles
 * Responsive design with Plyr integration
 */

.product-video-container {
  position: relative;
  margin: 1rem 0;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-playlist {
  position: relative;
  width: 100%;
}

/* Aspect ratio containers */
.video-playlist[data-aspect-ratio="16:9"] .video-item {
  aspect-ratio: 16 / 9;
}

.video-playlist[data-aspect-ratio="4:3"] .video-item {
  aspect-ratio: 4 / 3;
}

.video-playlist[data-aspect-ratio="1:1"] .video-item {
  aspect-ratio: 1 / 1;
}

.video-item {
  position: relative;
  width: 100%;
  background: #000;
}

.video-item video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Video info overlay */
.video-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 10;
}

/* Navigation controls */
.video-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.video-nav-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-nav-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.video-nav-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.video-nav-btn svg {
  width: 16px;
  height: 16px;
}

.video-counter {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
}

.current-video {
  font-weight: 700;
  color: #007bff;
}

/* Error state */
.video-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  z-index: 20;
}

.error-message {
  text-align: center;
  padding: 20px;
}

.error-message p {
  margin: 0 0 12px 0;
  font-size: 1rem;
}

.retry-btn {
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-btn:hover {
  background: #c82333;
}

/* Plyr customizations */
.plyr--video {
  border-radius: 8px 8px 0 0;
}

.plyr__control--overlaid {
  background: rgba(0, 123, 255, 0.9);
}

.plyr__control--overlaid:hover {
  background: rgba(0, 86, 179, 0.9);
}

/* Dark theme */
.product-video-container[data-theme="dark"] .video-navigation {
  background: #343a40;
  border-top-color: #495057;
}

.product-video-container[data-theme="dark"] .video-counter {
  color: #adb5bd;
}

.product-video-container[data-theme="dark"] .current-video {
  color: #17a2b8;
}

/* Light theme */
.product-video-container[data-theme="light"] .video-navigation {
  background: #ffffff;
  border-top-color: #dee2e6;
}

/* Responsive design */
@media (max-width: 768px) {
  .product-video-container {
    margin: 0.5rem 0;
    border-radius: 6px;
  }
  
  .video-navigation {
    padding: 10px 12px;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .video-nav-btn {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
  
  .video-nav-btn svg {
    width: 14px;
    height: 14px;
  }
  
  .video-counter {
    font-size: 0.8rem;
    order: 3;
    width: 100%;
    text-align: center;
    margin-top: 4px;
  }
}

@media (max-width: 480px) {
  .product-video-container {
    border-radius: 4px;
  }
  
  .video-navigation {
    padding: 8px 10px;
  }
  
  .video-nav-btn {
    padding: 5px 8px;
    font-size: 0.75rem;
  }
  
  .video-info {
    top: 8px;
    right: 8px;
    padding: 3px 6px;
    font-size: 0.7rem;
  }
}

/* Loading state */
.video-item.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 15;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus states for accessibility */
.video-nav-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.plyr__control:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .video-navigation {
    border-top-width: 2px;
  }
  
  .video-nav-btn {
    border: 2px solid transparent;
  }
  
  .video-nav-btn:focus {
    border-color: currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .video-nav-btn {
    transition: none;
  }
  
  .video-nav-btn:hover:not(:disabled) {
    transform: none;
  }
  
  .video-item.loading::before {
    animation: none;
  }
}
