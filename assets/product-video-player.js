/**
 * Product Video Player with <PERSON>lyr
 * Handles video playback, navigation, and view tracking
 */

class ProductVideoPlayer {
  constructor(container) {
    this.container = container;
    this.productId = container.dataset.productId;
    this.apiEndpoint = container.dataset.apiEndpoint;
    this.enableTracking = container.dataset.enableTracking === 'true';
    this.theme = container.dataset.theme || 'default';
    
    this.players = [];
    this.currentVideoIndex = 0;
    this.viewTracked = new Set(); // Track which videos have been viewed
    
    this.init();
  }

  init() {
    // Wait for Plyr library to be loaded
    if (window.Plyr) {
      this.initializePlayers();
    } else {
      document.addEventListener('plyrLibraryReady', () => {
        this.initializePlayers();
      });
    }
  }

  initializePlayers() {
    const videoElements = this.container.querySelectorAll('.plyr-video');
    
    videoElements.forEach((video, index) => {
      // Configure Plyr options based on theme and settings
      const plyrOptions = {
        controls: [
          'play-large', 'play', 'progress', 'current-time', 'duration',
          'mute', 'volume', 'settings', 'fullscreen'
        ],
        settings: ['quality', 'speed'],
        quality: {
          default: 720,
          options: [1080, 720, 480, 360]
        },
        speed: {
          selected: 1,
          options: [0.5, 0.75, 1, 1.25, 1.5, 2]
        }
      };

      // Apply theme
      if (this.theme === 'dark') {
        video.classList.add('plyr--dark');
      } else if (this.theme === 'light') {
        video.classList.add('plyr--light');
      }

      // Initialize Plyr
      const player = new Plyr(video, plyrOptions);
      this.players.push(player);

      // Add event listeners
      this.addPlayerEventListeners(player, index);

      // Hide all videos except the first one
      if (index > 0) {
        video.closest('.video-item').style.display = 'none';
      }
    });

    this.setupNavigation();
    this.updateNavigationState();
  }

  addPlayerEventListeners(player, index) {
    // Track when video starts playing
    player.on('play', () => {
      this.trackVideoView(index);
    });

    // Track significant watch time (25% of video)
    player.on('timeupdate', () => {
      const progress = (player.currentTime / player.duration) * 100;
      if (progress >= 25 && !this.viewTracked.has(`${index}-25`)) {
        this.viewTracked.add(`${index}-25`);
        this.trackVideoView(index, '25_percent');
      }
      if (progress >= 50 && !this.viewTracked.has(`${index}-50`)) {
        this.viewTracked.add(`${index}-50`);
        this.trackVideoView(index, '50_percent');
      }
      if (progress >= 75 && !this.viewTracked.has(`${index}-75`)) {
        this.viewTracked.add(`${index}-75`);
        this.trackVideoView(index, '75_percent');
      }
    });

    // Track video completion
    player.on('ended', () => {
      this.trackVideoView(index, 'completed');
      // Auto-advance to next video if available
      if (this.currentVideoIndex < this.players.length - 1) {
        setTimeout(() => {
          this.showVideo(this.currentVideoIndex + 1);
        }, 1000);
      }
    });

    // Handle errors
    player.on('error', (event) => {
      console.error('Video player error:', event);
      this.showVideoError(index);
    });
  }

  setupNavigation() {
    const prevBtn = this.container.querySelector('.prev-video');
    const nextBtn = this.container.querySelector('.next-video');

    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        this.showPreviousVideo();
      });
    }

    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        this.showNextVideo();
      });
    }
  }

  showVideo(index) {
    if (index < 0 || index >= this.players.length) return;

    // Hide current video
    const currentVideo = this.container.querySelector(`.video-item[data-video-index="${this.currentVideoIndex}"]`);
    if (currentVideo) {
      currentVideo.style.display = 'none';
      // Pause current video
      if (this.players[this.currentVideoIndex]) {
        this.players[this.currentVideoIndex].pause();
      }
    }

    // Show new video
    const newVideo = this.container.querySelector(`.video-item[data-video-index="${index}"]`);
    if (newVideo) {
      newVideo.style.display = 'block';
      this.currentVideoIndex = index;
      this.updateNavigationState();
      this.updateVideoCounter();
    }
  }

  showPreviousVideo() {
    if (this.currentVideoIndex > 0) {
      this.showVideo(this.currentVideoIndex - 1);
    }
  }

  showNextVideo() {
    if (this.currentVideoIndex < this.players.length - 1) {
      this.showVideo(this.currentVideoIndex + 1);
    }
  }

  updateNavigationState() {
    const prevBtn = this.container.querySelector('.prev-video');
    const nextBtn = this.container.querySelector('.next-video');

    if (prevBtn) {
      prevBtn.disabled = this.currentVideoIndex === 0;
    }

    if (nextBtn) {
      nextBtn.disabled = this.currentVideoIndex === this.players.length - 1;
    }
  }

  updateVideoCounter() {
    const currentSpan = this.container.querySelector('.current-video');
    if (currentSpan) {
      currentSpan.textContent = this.currentVideoIndex + 1;
    }
  }

  trackVideoView(videoIndex, eventType = 'play') {
    if (!this.enableTracking || !this.apiEndpoint) return;

    const videoElement = this.players[videoIndex]?.media;
    const videoId = videoElement?.dataset.videoId;
    const videoUrl = videoElement?.dataset.videoUrl;

    if (!videoId) return;

    const viewData = {
      product_id: this.productId,
      video_id: videoId,
      video_url: videoUrl,
      video_index: videoIndex,
      event_type: eventType,
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
      referrer: document.referrer
    };

    // Send view data to API
    this.sendViewData(viewData);
  }

  async sendViewData(data) {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('View tracked successfully:', result);
    } catch (error) {
      console.error('Failed to track video view:', error);
      // Optionally store failed requests for retry
      this.storeFailedRequest(data);
    }
  }

  storeFailedRequest(data) {
    // Store failed requests in localStorage for retry later
    try {
      const failedRequests = JSON.parse(localStorage.getItem('failedVideoViews') || '[]');
      failedRequests.push(data);
      // Keep only last 50 failed requests
      if (failedRequests.length > 50) {
        failedRequests.splice(0, failedRequests.length - 50);
      }
      localStorage.setItem('failedVideoViews', JSON.stringify(failedRequests));
    } catch (error) {
      console.error('Failed to store failed request:', error);
    }
  }

  showVideoError(index) {
    const videoItem = this.container.querySelector(`.video-item[data-video-index="${index}"]`);
    if (videoItem) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'video-error';
      errorDiv.innerHTML = `
        <div class="error-message">
          <p>Unable to load video</p>
          <button class="retry-btn" onclick="location.reload()">Retry</button>
        </div>
      `;
      videoItem.appendChild(errorDiv);
    }
  }

  // Retry failed requests
  static retryFailedRequests() {
    try {
      const failedRequests = JSON.parse(localStorage.getItem('failedVideoViews') || '[]');
      if (failedRequests.length === 0) return;

      failedRequests.forEach(async (data, index) => {
        try {
          const response = await fetch(data.api_endpoint || '/api/video-views', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
          });

          if (response.ok) {
            // Remove successful request from failed list
            failedRequests.splice(index, 1);
          }
        } catch (error) {
          console.error('Retry failed for request:', error);
        }
      });

      // Update localStorage with remaining failed requests
      localStorage.setItem('failedVideoViews', JSON.stringify(failedRequests));
    } catch (error) {
      console.error('Error retrying failed requests:', error);
    }
  }
}

// Initialize video players when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  const videoContainers = document.querySelectorAll('.product-video-container');
  
  videoContainers.forEach(container => {
    new ProductVideoPlayer(container);
  });

  // Retry failed requests on page load
  ProductVideoPlayer.retryFailedRequests();
});

// Also initialize for dynamically added content (AJAX cart, quick view, etc.)
document.addEventListener('shopify:section:load', function(event) {
  const videoContainers = event.target.querySelectorAll('.product-video-container');
  
  videoContainers.forEach(container => {
    new ProductVideoPlayer(container);
  });
});
