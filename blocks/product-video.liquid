
{% endif %}
<product-video data-video="{
          &quot;url&quot;: &quot;https:\/\/www.youtube.com\/watch?v=VRNaJWvcyik\u0026list=RDVRNaJWvcyik\u0026start_radio=1&quot;,
          &quot;title&quot;: &quot;Lo-fi Deep Focus Work and Study 💻 Smooth Lofi Beats to Boost Concentration | the witcher&quot;,
          &quot;description&quot;: &quot;a&quot;,
          &quot;thumbnail&quot;: &quot;https:\/\/img.youtube.com\/vi\/VRNaJWvcyik\/hqdefault.jpg&quot;
        }" data-show-title="true" data-show-description="true" data-autoplay="true" data-muted="true" data-block-id="AWjM2bTNUTG9haEtFV__autshopapp3_local_product_video_WFyJAR" data-section-id="template--17011362267444__1751016870cd0543a9" class="product-video-item" loading="lazy"><div class="product-video"><div class="video-container"><div class="plyr plyr--full-ui plyr--video plyr--youtube plyr--fullscreen-enabled plyr--paused plyr--stopped plyr__poster-enabled" id="player-muc4ndggp"><div class="plyr__controls"><button class="plyr__controls__item plyr__control" type="button" data-plyr="play" aria-pressed="false" aria-label="Play, Lo-fi Deep Focus Work and Study 💻 Smooth Lofi Beats to Boost Concentration | the witcher"><svg class="icon--pressed" aria-hidden="true" focusable="false"><use xlink:href="#plyr-pause"></use></svg><svg class="icon--not-pressed" aria-hidden="true" focusable="false"><use xlink:href="#plyr-play"></use></svg><span class="label--pressed plyr__tooltip">Pause</span><span class="label--not-pressed plyr__tooltip">Play</span></button><div class="plyr__controls__item plyr__progress__container"><div class="plyr__progress"><input data-plyr="seek" type="range" min="0" max="100" step="0.01" value="0" autocomplete="off" role="slider" aria-label="Seek" aria-valuemin="0" aria-valuemax="4221" aria-valuenow="0" id="plyr-seek-1682" aria-valuetext="00:00 of 1:10:21" style="--value: 0%;"><progress class="plyr__progress__buffer" min="0" max="100" value="0" role="progressbar" aria-hidden="true">% buffered</progress><span class="plyr__tooltip">00:00</span></div></div><div class="plyr__controls__item plyr__time--current plyr__time" aria-label="Current time" role="timer">00:00</div><div class="plyr__controls__item plyr__time--duration plyr__time" aria-label="Duration" role="timer">1:10:21</div><div class="plyr__controls__item plyr__volume"><button type="button" class="plyr__control" data-plyr="mute" aria-pressed="false"><svg class="icon--pressed" aria-hidden="true" focusable="false"><use xlink:href="#plyr-muted"></use></svg><svg class="icon--not-pressed" aria-hidden="true" focusable="false"><use xlink:href="#plyr-volume"></use></svg><span class="label--pressed plyr__tooltip">Unmute</span><span class="label--not-pressed plyr__tooltip">Mute</span></button><input data-plyr="volume" type="range" min="0" max="1" step="0.05" value="1" autocomplete="off" role="slider" aria-label="Volume" aria-valuemin="0" aria-valuemax="100" aria-valuenow="50" id="plyr-volume-1682" aria-valuetext="50.0%" style="--value: 50%;"></div><div class="plyr__controls__item plyr__menu"><button aria-haspopup="true" aria-controls="plyr-settings-1682" aria-expanded="false" type="button" class="plyr__control" data-plyr="settings" aria-pressed="false"><svg aria-hidden="true" focusable="false"><use xlink:href="#plyr-settings"></use></svg><span class="plyr__tooltip">Settings</span></button><div class="plyr__menu__container" id="plyr-settings-1682" hidden=""><div><div id="plyr-settings-1682-home"><div role="menu"><button data-plyr="settings" type="button" class="plyr__control plyr__control--forward" role="menuitem" aria-haspopup="true" hidden=""><span>Quality<span class="plyr__menu__value">undefined</span></span></button><button data-plyr="settings" type="button" class="plyr__control plyr__control--forward" role="menuitem" aria-haspopup="true"><span>Speed<span class="plyr__menu__value">Normal</span></span></button></div></div><div id="plyr-settings-1682-quality" hidden=""><button type="button" class="plyr__control plyr__control--back"><span aria-hidden="true">Quality</span><span class="plyr__sr-only">Go back to previous menu</span></button><div role="menu"></div></div><div id="plyr-settings-1682-speed" hidden=""><button type="button" class="plyr__control plyr__control--back"><span aria-hidden="true">Speed</span><span class="plyr__sr-only">Go back to previous menu</span></button><div role="menu"><button data-plyr="speed" type="button" role="menuitemradio" class="plyr__control" aria-checked="false" value="0.5"><span>0.5×</span></button><button data-plyr="speed" type="button" role="menuitemradio" class="plyr__control" aria-checked="false" value="0.75"><span>0.75×</span></button><button data-plyr="speed" type="button" role="menuitemradio" class="plyr__control" aria-checked="true" value="1"><span>Normal</span></button><button data-plyr="speed" type="button" role="menuitemradio" class="plyr__control" aria-checked="false" value="1.25"><span>1.25×</span></button><button data-plyr="speed" type="button" role="menuitemradio" class="plyr__control" aria-checked="false" value="1.5"><span>1.5×</span></button><button data-plyr="speed" type="button" role="menuitemradio" class="plyr__control" aria-checked="false" value="1.75"><span>1.75×</span></button><button data-plyr="speed" type="button" role="menuitemradio" class="plyr__control" aria-checked="false" value="2"><span>2×</span></button></div></div></div></div></div><button class="plyr__controls__item plyr__control" type="button" data-plyr="fullscreen" aria-pressed="false"><svg class="icon--pressed" aria-hidden="true" focusable="false"><use xlink:href="#plyr-exit-fullscreen"></use></svg><svg class="icon--not-pressed" aria-hidden="true" focusable="false"><use xlink:href="#plyr-enter-fullscreen"></use></svg><span class="label--pressed plyr__tooltip">Exit fullscreen</span><span class="label--not-pressed plyr__tooltip">Enter fullscreen</span></button></div><div class="plyr__video-wrapper plyr__video-embed youtube-controls-hidden" style="aspect-ratio: 16 / 9;"><iframe id="youtube-7467" frameborder="0" allowfullscreen="true" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" title="Player for Lo-fi Deep Focus Work and Study 💻 Smooth Lofi Beats to Boost Concentration | the witcher" width="640" height="360" src="https://www.youtube.com/embed/VRNaJWvcyik?autoplay=1&amp;controls=0&amp;disablekb=1&amp;playsinline=1&amp;cc_load_policy=0&amp;cc_lang_pref=auto&amp;widget_referrer=https%3A%2F%2Fautoserve-dev1.myshopify.com%2Fproducts%2Ftest-product-with-inventory-stock&amp;rel=0&amp;showinfo=0&amp;iv_load_policy=3&amp;modestbranding=1&amp;customControls=true&amp;noCookie=false&amp;enablejsapi=1&amp;origin=https%3A%2F%2Fautoserve-dev1.myshopify.com&amp;widgetid=2&amp;forigin=https%3A%2F%2Fautoserve-dev1.myshopify.com%2Fproducts%2Ftest-product-with-inventory-stock&amp;aoriginsup=1&amp;gporigin=https%3A%2F%2Fautoserve-dev1.myshopify.com%2Fcollections%2Fall&amp;vf=1" webkitallowfullscreen="true" mozallowfullscreen="true"></iframe><div class="plyr__poster" style="background-image: url(&quot;https://i.ytimg.com/vi/VRNaJWvcyik/maxresdefault.jpg&quot;);"></div></div><button type="button" class="plyr__control plyr__control--overlaid" data-plyr="play" aria-pressed="false" aria-label="Play, Lo-fi Deep Focus Work and Study 💻 Smooth Lofi Beats to Boost Concentration | the witcher"><svg aria-hidden="true" focusable="false"><use xlink:href="#plyr-play"></use></svg><span class="plyr__sr-only">Play</span></button></div></div><h3 class="video-title">Lo-fi Deep Focus Work and Study 💻 Smooth Lofi Beats to Boost Concentration | the witcher</h3><div class="video-description">a</div></div></product-video>
{% schema %}
{
  "name": "Product Video Player",
  "target": "section",
  "available_if": "{{ product != blank }}",
  "settings": [
    {
      "type": "header",
      "content": "Video Source"
    },
    {
      "type": "text",
      "id": "metafield_namespace",
      "label": "Metafield Namespace",
      "default": "custom",
      "info": "Namespace of the product metafield containing video URLs"
    },
    {
      "type": "text",
      "id": "metafield_key", 
      "label": "Metafield Key",
      "default": "video_urls",
      "info": "Key of the product metafield containing comma-separated video URLs"
    },
    {
      "type": "header",
      "content": "Player Settings"
    },
    {
      "type": "checkbox",
      "id": "enable_autoplay",
      "label": "Enable Autoplay",
      "default": false,
      "info": "Auto-play first video (muted by default)"
    },
    {
      "type": "checkbox",
      "id": "enable_controls",
      "label": "Show Controls",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_muted",
      "label": "Mute by Default", 
      "default": true
    },
    {
      "type": "range",
      "id": "video_width",
      "label": "Video Width",
      "min": 50,
      "max": 100,
      "step": 5,
      "default": 100,
      "unit": "%"
    },
    {
      "type": "select",
      "id": "video_aspect_ratio",
      "label": "Aspect Ratio",
      "options": [
        { "value": "16:9", "label": "16:9 (Widescreen)" },
        { "value": "4:3", "label": "4:3 (Standard)" },
        { "value": "1:1", "label": "1:1 (Square)" }
      ],
      "default": "16:9"
    },
    {
      "type": "select",
      "id": "player_theme",
      "label": "Player Theme",
      "options": [
        { "value": "default", "label": "Default" },
        { "value": "dark", "label": "Dark" },
        { "value": "light", "label": "Light" }
      ],
      "default": "default"
    },
    {
      "type": "header",
      "content": "View Tracking"
    },
    {
      "type": "checkbox",
      "id": "enable_view_tracking",
      "label": "Enable View Tracking",
      "default": true
    },
    {
      "type": "text",
      "id": "api_endpoint",
      "label": "API Endpoint",
      "default": "/api/video-views",
      "info": "Endpoint to send view count updates"
    }
  ],
  "presets": [
    {
      "name": "Product Video Player"
    }
  ]
}
{% endschema %}
